# Replace vs_update.exe
# Version 1.2.0
# pyinstaller --onefile --noconsole --clean vs_command_v1.2.0.py

import os
import subprocess
import time
import ctypes
import sys

# Hide console window
if sys.platform == 'win32':
    ctypes.windll.user32.ShowWindow(ctypes.windll.kernel32.GetConsoleWindow(), 0)

# Constants for file paths
SOURCE_PATHS = [
    r"\\vs-it-server\it-shared\!vs_tes",  # Hostname
    r"\\***********\it-shared\!vs_tes",   # Primary IP
    r"\\***********\it-shared\!vs_tes"    # Backup IP
]
USERNAME = "it-user"
PASSWORD = "1111"
NETWORK_DRIVE = "Z:"
SOURCE_FILE = r"Z:\vs_update\vs_update.exe"
DEST_PATH = r"C:\!vs_tes\vs_update"

def run_as_admin():
    """Restart the script with administrator privileges"""
    try:
        script = os.path.abspath(sys.argv[0])
        params = ' '.join([f'"{arg}"' for arg in sys.argv[1:]])
        ret = ctypes.windll.shell32.ShellExecuteW(None, "runas", sys.executable, f'"{script}" {params}', None, 1)
        if ret > 32:
            sys.exit(0)
        return False
    except Exception as e:
        return False

def map_network_drive():
    """Map network drive using net use command with fallback paths"""
    try:
        # First ensure any existing mapping is removed
        subprocess.run(f"net use {NETWORK_DRIVE} /delete /y", shell=True, capture_output=True)
        time.sleep(1)  # Short delay after deletion

        # Try each network path in order
        for network_path in SOURCE_PATHS:
            try:
                # Map the network drive
                result = subprocess.run(
                    f'net use {NETWORK_DRIVE} "{network_path}" /user:{USERNAME} "{PASSWORD}"',
                    shell=True,
                    capture_output=True,
                    text=True
                )

                if result.returncode == 0:
                    print(f"Successfully mapped network drive using: {network_path}")
                    return True
                else:
                    print(f"Failed to map {network_path}: {result.stderr}")

            except Exception as e:
                print(f"Error trying to map {network_path}: {e}")
                continue

        print("Failed to map network drive using any of the available paths")
        return False

    except Exception as e:
        print(f"Failed to map network drive: {e}")
        return False

def unmap_network_drive():
    """Unmap network drive"""
    try:
        subprocess.run(f"net use {NETWORK_DRIVE} /delete /y", shell=True, capture_output=True)
    except:
        pass

def copy_update_file():
    """Copy and replace vs_update.exe"""
    try:
        # Create destination directory if it doesn't exist
        os.makedirs(DEST_PATH, exist_ok=True)
        
        # Copy the file
        result = subprocess.run(
            f'xcopy "{SOURCE_FILE}" "{DEST_PATH}" /Y',
            shell=True,
            capture_output=True,
            text=True
        )
        
        return result.returncode == 0
    except Exception as e:
        print(f"Failed to copy update file: {e}")
        return False

def main():
    """Main function that orchestrates the entire process"""
    # Check for admin privileges
    if not ctypes.windll.shell32.IsUserAnAdmin():
        if not run_as_admin():
            print("This script requires administrator privileges.")
            return
        return

    try:
        # Map network drive
        if not map_network_drive():
            print("Failed to map network drive")
            return

        # Copy update file
        if not copy_update_file():
            print("Failed to copy update file")
            return

    finally:
        # Always try to unmap the network drive
        unmap_network_drive()
        
        # Wait before exiting
        time.sleep(10)

if __name__ == "__main__":
    main()